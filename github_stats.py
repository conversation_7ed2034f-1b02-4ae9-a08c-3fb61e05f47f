#!/usr/bin/env python3
"""
Get commit statistics from GitHub API
"""
import requests
import json
import sys
from collections import defaultdict

# Configuration
GITHUB_TOKEN = "*********************************************************************************************"  # Replace with your token
REPO_OWNER = "TongNguyenvk"
REPO_NAME = "CNPM_WebSiteDKKhamBenh"


def calculate_gini_coefficient(values):
    """Calculate Gini coefficient from a list of values"""
    if not values or len(values) <= 1:
        return 0.0

    n = len(values)
    total = sum(values)

    if total == 0:
        return 0.0

    # Sort values in ascending order
    sorted_values = sorted(values)

    # Calculate Gini coefficient using the standard formula
    index = 0
    for i in range(n):
        index += (2 * (i + 1) - n - 1) * sorted_values[i]

    return index / (n * total)


def interpret_gini(gini_value):
    """Interpret Gini coefficient value"""
    if gini_value < 0.3:
        return "Very balanced - evenly distributed"
    elif gini_value < 0.5:
        return "Moderately balanced - some inequality but acceptable"
    elif gini_value < 0.7:
        return "Unbalanced - significant inequality"
    else:
        return "Highly unbalanced - very unequal distribution"


def get_github_data(endpoint, params=None):
    """Make GitHub API request"""
    url = f"https://api.github.com/repos/{REPO_OWNER}/{REPO_NAME}/{endpoint}"
    headers = {
        "Authorization": f"token {GITHUB_TOKEN}",
        "Accept": "application/vnd.github.v3+json",
    }

    try:
        response = requests.get(url, headers=headers, params=params)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"Error fetching data: {e}")
        return None


def get_all_commits():
    """Get all commits from GitHub API"""
    commits = []
    page = 1
    per_page = 100

    print("📥 Fetching commits from GitHub API...")

    while True:
        params = {"per_page": per_page, "page": page}

        data = get_github_data("commits", params)
        if not data or len(data) == 0:
            break

        commits.extend(data)
        print(f"   Fetched page {page}: {len(data)} commits")

        if len(data) < per_page:
            break

        page += 1

    print(f"✅ Total commits fetched: {len(commits)}")
    return commits


def get_contributor_stats():
    """Get contributor statistics from GitHub API"""
    print("📊 Fetching contributor statistics...")

    data = get_github_data("stats/contributors")
    if not data:
        return None

    contributors = []
    for contributor in data:
        contributors.append(
            {
                "author": contributor["author"]["login"],
                "name": contributor["author"]["login"],
                "commits": contributor["total"],
                "additions": sum(week["a"] for week in contributor["weeks"]),
                "deletions": sum(week["d"] for week in contributor["weeks"]),
            }
        )

    return contributors


def analyze_commits_by_author():
    """Analyze commits by author using GitHub API"""
    commits = get_all_commits()
    if not commits:
        return

    # Count commits by author
    author_commits = defaultdict(int)
    commit_messages = defaultdict(int)

    for commit in commits:
        if commit.get("author") and commit["author"].get("login"):
            author = commit["author"]["login"]
            author_commits[author] += 1

        # Count commit messages
        message = commit["commit"]["message"].split("\n")[0]  # First line only
        commit_messages[message] += 1

    # Calculate Gini coefficients
    contributor_gini = calculate_gini_coefficient(list(author_commits.values()))
    message_gini = calculate_gini_coefficient(list(commit_messages.values()))

    # Display results
    print(f"\n=== GITHUB API COMMIT ANALYSIS ===")
    print(f"Total commits: {len(commits)}")
    print(f"Number of contributors: {len(author_commits)}")
    print(f"Unique commit messages: {len(commit_messages)}")

    print(f"\n=== GINI INDEX ANALYSIS ===")
    print(f"Contributor Gini Index: {contributor_gini:.4f}")
    print(f"Interpretation: {interpret_gini(contributor_gini)}")
    print(f"Message Gini Index: {message_gini:.4f}")
    print(f"Interpretation: {interpret_gini(message_gini)}")

    print(f"\n=== CONTRIBUTOR DISTRIBUTION ===")
    sorted_contributors = sorted(
        author_commits.items(), key=lambda x: x[1], reverse=True
    )

    for i, (author, count) in enumerate(sorted_contributors, 1):
        percentage = (count / len(commits)) * 100
        print(f"{i:2d}. {author:<25} {count:4d} commits ({percentage:5.1f}%)")

    # Show most common commit messages
    print(f"\n=== MOST COMMON COMMIT MESSAGES ===")
    sorted_messages = sorted(commit_messages.items(), key=lambda x: x[1], reverse=True)

    for i, (message, count) in enumerate(sorted_messages[:10], 1):
        percentage = (count / len(commits)) * 100
        display_message = message[:50] + "..." if len(message) > 50 else message
        print(f"{i:2d}. ({count:2d} times, {percentage:4.1f}%) {display_message}")


def get_detailed_contributor_stats():
    """Get detailed contributor statistics"""
    contributors = get_contributor_stats()
    if not contributors:
        return

    print(f"\n=== DETAILED CONTRIBUTOR STATISTICS ===")
    total_commits = sum(c["commits"] for c in contributors)
    total_additions = sum(c["additions"] for c in contributors)
    total_deletions = sum(c["deletions"] for c in contributors)

    print(f"Total commits: {total_commits}")
    print(f"Total additions: {total_additions:,}")
    print(f"Total deletions: {total_deletions:,}")

    # Calculate Gini coefficients for different metrics
    commit_counts = [c["commits"] for c in contributors]
    addition_counts = [c["additions"] for c in contributors]
    deletion_counts = [c["deletions"] for c in contributors]

    commit_gini = calculate_gini_coefficient(commit_counts)
    addition_gini = calculate_gini_coefficient(addition_counts)
    deletion_gini = calculate_gini_coefficient(deletion_counts)

    print(f"\n=== CONTRIBUTION GINI ANALYSIS ===")
    print(f"Commit Gini Index: {commit_gini:.4f} - {interpret_gini(commit_gini)}")
    print(f"Addition Gini Index: {addition_gini:.4f} - {interpret_gini(addition_gini)}")
    print(f"Deletion Gini Index: {deletion_gini:.4f} - {interpret_gini(deletion_gini)}")

    print(
        f"\n{'Author':<20} {'Commits':<8} {'Additions':<10} {'Deletions':<10} {'Net':<10}"
    )
    print("-" * 70)

    for contributor in sorted(contributors, key=lambda x: x["commits"], reverse=True):
        net_changes = contributor["additions"] - contributor["deletions"]
        print(
            f"{contributor['name']:<20} {contributor['commits']:<8} "
            f"{contributor['additions']:<10,} {contributor['deletions']:<10,} "
            f"{net_changes:<10,}"
        )


def get_commit_activity():
    """Get commit activity over time"""
    print("📈 Fetching commit activity...")

    data = get_github_data("stats/commit_activity")
    if not data:
        return

    print(f"\n=== COMMIT ACTIVITY (Last 52 weeks) ===")
    total_commits = sum(week["total"] for week in data)
    print(f"Total commits in last 52 weeks: {total_commits}")

    # Show last 10 weeks
    print(f"\nLast 10 weeks:")
    for week in data[-10:]:
        week_date = week["week"]
        commits = week["total"]
        print(f"Week {week_date}: {commits} commits")


def main():
    if not GITHUB_TOKEN or GITHUB_TOKEN == "your_github_token_here":
        print("❌ Please set your GitHub token in the script")
        print("Get token from: https://github.com/settings/tokens")
        return

    if len(sys.argv) > 1:
        command = sys.argv[1]
        if command == "commits":
            analyze_commits_by_author()
        elif command == "contributors":
            get_detailed_contributor_stats()
        elif command == "activity":
            get_commit_activity()
        else:
            print("Usage: python github_stats.py [commits|contributors|activity]")
    else:
        # Run all analyses
        analyze_commits_by_author()
        get_detailed_contributor_stats()
        get_commit_activity()


if __name__ == "__main__":
    main()
